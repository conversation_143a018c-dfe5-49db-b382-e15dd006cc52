import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { FamilySwitcher } from '@/components/family/FamilySwitcher';

// Użyj globalnego mocka z jest.setup.js
declare global {
    var mockLocation: {
        href: string;
        assign: jest.Mock;
        replace: jest.Mock;
        reload: jest.Mock;
    };
    var resetLocationMock: () => void;
}

// Mock getBoundingClientRect
Element.prototype.getBoundingClientRect = jest.fn(() => ({
    width: 120,
    height: 40,
    top: 0,
    left: 0,
    bottom: 40,
    right: 120,
    x: 0,
    y: 0,
    toJSON: jest.fn(),
}));

describe('FamilySwitcher', () => {
    const mockFamiliesWithMembership = [
        {
            id: 'member-1',
            familyId: 'family-1',
            userId: 'user-1',
            role: 'OWNER' as const,
            isAdult: true,
            points: 150,
            joinedAt: new Date(),
            lastActive: new Date(),
            family: {
                id: 'family-1',
                name: '<PERSON><PERSON><PERSON>',
                description: 'Na<PERSON>a rodzina',
                createdAt: new Date(),
                updatedAt: new Date(),
                ownerId: 'user-1',
                _count: {
                    tasks: 5,
                    members: 3,
                },
            },
        },
        {
            id: 'member-2',
            familyId: 'family-2',
            userId: 'user-1',
            role: 'PARENT' as const,
            isAdult: true,
            points: 80,
            joinedAt: new Date(),
            lastActive: new Date(),
            family: {
                id: 'family-2',
                name: 'Rodzina Nowakowych',
                description: 'Druga rodzina',
                createdAt: new Date(),
                updatedAt: new Date(),
                ownerId: 'user-2',
                _count: {
                    tasks: 3,
                    members: 2,
                },
            },
        },
    ];

    beforeEach(() => {
        jest.clearAllMocks();
        resetLocationMock();
    });

    describe('renderowanie podstawowe', () => {
        it('powinien renderować przycisk z nazwą aktualnej rodziny', () => {
            render(
                <FamilySwitcher
                    families={mockFamiliesWithMembership}
                    currentFamilyId="family-1"
                />
            );

            const button = screen.getByRole('combobox');
            expect(button).toBeDefined();
            expect(screen.getByText('Rodzina Kowalskich')).toBeDefined();
        });

        it('powinien pokazać placeholder gdy brak aktualnej rodziny', () => {
            render(
                <FamilySwitcher
                    families={mockFamiliesWithMembership}
                    currentFamilyId={undefined}
                />
            );

            expect(screen.getByText('Wybierz rodzinę')).toBeDefined();
        });

        it('powinien pokazać przycisk tworzenia rodziny gdy brak rodzin', () => {
            render(<FamilySwitcher families={[]} currentFamilyId={undefined} />);

            expect(screen.getByText('Utwórz pierwszą rodzinę')).toBeDefined();
        });

        it('powinien pokazać przycisk tworzenia rodziny gdy families jest null', () => {
            render(<FamilySwitcher families={null} currentFamilyId={undefined} />);

            expect(screen.getByText('Utwórz pierwszą rodzinę')).toBeDefined();
        });
    });

    describe('lista rodzin', () => {
        it('powinien pokazać listę rodzin po kliknięciu', async () => {
            const user = userEvent.setup();
            render(
                <FamilySwitcher
                    families={mockFamiliesWithMembership}
                    currentFamilyId="family-1"
                />
            );

            const button = screen.getByRole('combobox');
            await user.click(button);

            await waitFor(() => {
                expect(screen.getAllByText('Rodzina Kowalskich')).toHaveLength(2); // jedna w przycisku, jedna w dropdown
                expect(screen.getByText('Rodzina Nowakowych')).toBeDefined();
            });
        });

        it('powinien oznaczyć aktualną rodzinę ikoną check', async () => {
            const user = userEvent.setup();
            render(
                <FamilySwitcher
                    families={mockFamiliesWithMembership}
                    currentFamilyId="family-1"
                />
            );

            const button = screen.getByRole('combobox');
            await user.click(button);

            await waitFor(() => {
                const checkIcon = document.querySelector('.lucide-check');
                expect(checkIcon).toBeDefined();
            });
        });

        it('powinien pokazać informacje o rodzinie (członkowie, zadania)', async () => {
            const user = userEvent.setup();
            render(
                <FamilySwitcher
                    families={mockFamiliesWithMembership}
                    currentFamilyId="family-1"
                />
            );

            const button = screen.getByRole('combobox');
            await user.click(button);

            await waitFor(() => {
                // Sprawdź czy są wyświetlane liczby członków i zadań w dropdown
                const memberCounts = screen.getAllByText('3');
                const taskCounts = screen.getAllByText('5');
                expect(memberCounts.length).toBeGreaterThan(0);
                expect(taskCounts.length).toBeGreaterThan(0);
            });
        });
    });

    describe('przełączanie rodzin', () => {
        it('powinien przekierować po wybraniu innej rodziny', async () => {
            const user = userEvent.setup();
            render(
                <FamilySwitcher
                    families={mockFamiliesWithMembership}
                    currentFamilyId="family-1"
                />
            );

            const button = screen.getByRole('combobox');
            await user.click(button);

            await waitFor(() => {
                expect(screen.getByText('Rodzina Nowakowych')).toBeDefined();
            });

            const secondFamily = screen.getByText('Rodzina Nowakowych');
            await user.click(secondFamily);

            expect(mockLocation.href).toBe('/families/family-2');
        });

        it('nie powinien przekierowywać po wybraniu tej samej rodziny', async () => {
            const user = userEvent.setup();
            render(
                <FamilySwitcher
                    families={mockFamiliesWithMembership}
                    currentFamilyId="family-1"
                />
            );

            const button = screen.getByRole('combobox');
            await user.click(button);

            await waitFor(() => {
                expect(screen.getAllByText('Rodzina Kowalskich')).toHaveLength(2);
            });

            // Kliknij na rodzinę w dropdown (nie w przycisku)
            const dropdownItems = screen.getAllByText('Rodzina Kowalskich');
            const currentFamilyInDropdown = dropdownItems.find(item =>
                item.closest('[role="menuitem"]')
            );
            expect(currentFamilyInDropdown).toBeDefined();
            await user.click(currentFamilyInDropdown!);

            expect(mockLocation.href).toBe('');
        });
    });

    describe('tworzenie nowej rodziny', () => {
        it('powinien pokazać opcję tworzenia nowej rodziny w dropdown', async () => {
            const user = userEvent.setup();
            render(
                <FamilySwitcher
                    families={mockFamiliesWithMembership}
                    currentFamilyId="family-1"
                />
            );

            const button = screen.getByRole('combobox');
            await user.click(button);

            await waitFor(() => {
                expect(screen.getByText('Utwórz nową rodzinę')).toBeDefined();
            });
        });

        it('powinien przekierować do formularza tworzenia rodziny', async () => {
            const user = userEvent.setup();
            render(
                <FamilySwitcher
                    families={mockFamiliesWithMembership}
                    currentFamilyId="family-1"
                />
            );

            const button = screen.getByRole('combobox');
            await user.click(button);

            await waitFor(() => {
                const createButton = screen.getByText('Utwórz nową rodzinę');
                expect(createButton).toBeDefined();
            });

            const createButton = screen.getByText('Utwórz nową rodzinę');
            await user.click(createButton);

            expect(mockLocation.href).toBe('/families/new');
        });

        it('powinien przekierować bezpośrednio gdy brak rodzin', async () => {
            const user = userEvent.setup();
            render(<FamilySwitcher families={[]} currentFamilyId={undefined} />);

            const createButton = screen.getByText('Utwórz pierwszą rodzinę');
            await user.click(createButton);

            expect(mockLocation.href).toBe('/families/new');
        });
    });

    describe('dostępność', () => {
        it('powinien mieć odpowiednie atrybuty ARIA', () => {
            render(
                <FamilySwitcher
                    families={mockFamiliesWithMembership}
                    currentFamilyId="family-1"
                />
            );

            const button = screen.getByRole('combobox');
            expect(button.getAttribute('role')).toBe('combobox');
            expect(button.getAttribute('aria-expanded')).toBe('false');
        });

        it('powinien być dostępny przez klawiaturę', async () => {
            const user = userEvent.setup();
            render(
                <FamilySwitcher
                    families={mockFamiliesWithMembership}
                    currentFamilyId="family-1"
                />
            );

            // Nawigacja do przycisku
            await user.tab();
            const button = screen.getByRole('combobox');
            expect(document.activeElement).toBe(button);

            // Otwarcie listy klawiszem Enter
            await user.keyboard('{Enter}');

            await waitFor(() => {
                expect(button.getAttribute('aria-expanded')).toBe('true');
            });
        });

        it('powinien pokazać informacje o roli użytkownika', () => {
            render(
                <FamilySwitcher
                    families={mockFamiliesWithMembership}
                    currentFamilyId="family-1"
                />
            );

            expect(screen.getByText('Właściciel')).toBeDefined();
        });
    });
});
